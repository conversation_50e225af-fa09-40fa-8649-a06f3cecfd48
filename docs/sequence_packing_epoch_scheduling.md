# Sequence Packing with Epoch-Based Scheduling

## Problem Description

When using sequence packing (`sequence_packing: true`) with epoch-based duration (`max_duration: 2ep`), you may encounter the following error:

```
RuntimeError: Cannot convert time, as state.dataloader_len is None.
```

This error occurs during learning rate scheduler initialization when trying to convert epoch-based durations to batch-based durations.

## Root Cause

The issue stems from the fundamental incompatibility between sequence packing and epoch-based scheduling:

1. **Sequence Packer Returns `None` for Length**: The `SequencePacker.__len__()` method returns `None` because the number of output batches per epoch is unpredictable due to dynamic sequence packing.

2. **Composer Needs Dataloader Length for Epoch Conversion**: Composer's `_convert_time` function requires `state.dataloader_len` to convert epoch-based durations (like `2ep`) to batch-based durations.

3. **Dynamic Packing Makes Length Unpredictable**: Since sequence packing dynamically combines sequences based on their lengths, the exact number of batches per epoch cannot be determined ahead of time.

## Solutions Implemented

### Solution 1: Dataloader Length Estimation (Recommended)

**What it does**: Provides a reasonable estimate of the packed dataset length to enable epoch-based scheduling.

**Implementation**: Modified `SequencePacker.__len__()` to return an estimated length based on:
- Source dataloader length
- Estimated packing efficiency (80% conservative estimate)

**Benefits**:
- ✅ Allows epoch-based duration (`max_duration: 2ep`) to work with sequence packing
- ✅ Maintains epoch-based masking refresh behavior
- ✅ No configuration changes required
- ✅ Conservative estimate ensures training doesn't stop prematurely

**Limitations**:
- ⚠️ Length is an approximation (actual length may vary by ±20%)
- ⚠️ Training might stop slightly before or after the exact target

### Solution 2: Epoch Simulation (Advanced)

**What it does**: Implements virtual epochs within the sequence packer to refresh masking patterns periodically.

**Implementation**: Added new parameters to sequence packer:
- `simulate_epochs`: Enable virtual epoch simulation
- `tokens_per_epoch`: Number of tokens to process before refreshing masking

**Usage**:
```yaml
train_loader:
  sequence_packing: true
  simulate_epochs: true
  tokens_per_epoch: 1000000000  # Adjust based on your dataset
```

**Benefits**:
- ✅ Precise control over masking refresh frequency
- ✅ Works with batch-based duration
- ✅ Maintains training efficiency of sequence packing

**Limitations**:
- ⚠️ Requires manual calculation of tokens per epoch
- ⚠️ Must use batch-based duration (`max_duration: XYZba`)

### Solution 3: Batch-Based Duration (Fallback)

**What it does**: Use batch-based duration instead of epoch-based duration.

**Usage**:
```yaml
max_duration: 100000ba  # Instead of 2ep
```

**Benefits**:
- ✅ Immediate fix with no code changes
- ✅ Precise control over training duration
- ✅ Works reliably with sequence packing

**Limitations**:
- ❌ Loses epoch-based masking refresh
- ❌ Need to estimate equivalent batch count
- ❌ Less intuitive than epoch-based scheduling

## Current Configuration

Your YAML file now uses **Solution 1** (length estimation), which allows:

```yaml
max_duration: 2ep  # Now works with sequence packing
sequence_packing: true
```

## Recommendations

1. **For most users**: Use the current configuration with length estimation (Solution 1)
2. **For precise control**: Consider Solution 2 with epoch simulation
3. **For immediate workaround**: Use Solution 3 with batch-based duration

## Masking Refresh Behavior

### With Epoch-Based Duration (Solutions 1 & 2)
- Masking patterns refresh at epoch boundaries
- Different tokens are masked in each epoch
- Improves training quality by exposing model to varied masking patterns

### With Batch-Based Duration (Solution 3)
- Masking patterns may not refresh during training
- Same tokens might be consistently masked
- Potentially reduced training quality

## Monitoring and Adjustment

When using length estimation (Solution 1):
- Monitor training progress to ensure it completes as expected
- If training stops too early/late, you can adjust the packing efficiency estimate
- Consider switching to batch-based duration for production runs requiring precise timing

## Technical Details

The length estimation uses a conservative 80% packing efficiency:
```python
estimated_length = int(src_dataloader_len * 0.8)
```

This accounts for the fact that sequence packing typically achieves 70-90% efficiency depending on sequence length distribution in your dataset.
