#!/usr/bin/env python3
"""
Test script to verify that sequence packing works with epoch-based scheduling.

This script tests the solutions implemented for the sequence packing + epoch scheduling issue.
"""

import sys
import os
import tempfile
import numpy as np
import torch
from torch.utils.data import DataLoader, Dataset

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from sequence_packer import GreedyBestFitSequencePacker


class MockDataset(Dataset):
    """Mock dataset for testing."""
    
    def __init__(self, num_samples=1000, max_seq_len=512):
        self.num_samples = num_samples
        self.max_seq_len = max_seq_len
    
    def __len__(self):
        return self.num_samples
    
    def __getitem__(self, idx):
        # Generate random sequence lengths between 50 and max_seq_len
        seq_len = np.random.randint(50, self.max_seq_len)
        # Generate random token IDs (avoiding special tokens)
        tokens = np.random.randint(10, 1000, size=seq_len).tolist()
        return tokens


def test_sequence_packer_length_estimation():
    """Test that sequence packer provides length estimation."""
    print("Testing sequence packer length estimation...")
    
    # Create mock dataset and dataloader
    dataset = MockDataset(num_samples=1000)
    dataloader = DataLoader(dataset, batch_size=32, shuffle=False)
    
    # Create sequence packer
    packer = GreedyBestFitSequencePacker.from_composer(
        src_iterable=dataloader,
        batch_size=128,
        micro_batch_size=32,
        max_seq_len=512,
        buffer_size=640,
        mask_token_id=103,
        pad_token_id=0,
        mask_prob=0.15,
        seed=42,
    )
    
    # Test length estimation
    estimated_length = len(packer)
    print(f"Source dataloader length: {len(dataloader)}")
    print(f"Estimated packed length: {estimated_length}")
    
    if estimated_length is not None:
        print("✅ Length estimation working - epoch-based scheduling should work")
        
        # Verify the estimate is reasonable
        expected_min = int(len(dataloader) * 0.6)  # 60% efficiency (conservative)
        expected_max = int(len(dataloader) * 0.9)  # 90% efficiency (optimistic)
        
        if expected_min <= estimated_length <= expected_max:
            print(f"✅ Length estimate ({estimated_length}) is within reasonable range [{expected_min}, {expected_max}]")
        else:
            print(f"⚠️ Length estimate ({estimated_length}) outside expected range [{expected_min}, {expected_max}]")
    else:
        print("❌ Length estimation not working - epoch-based scheduling will fail")
    
    return estimated_length is not None


def test_epoch_simulation():
    """Test epoch simulation functionality."""
    print("\nTesting epoch simulation...")
    
    # Create mock dataset and dataloader
    dataset = MockDataset(num_samples=100)  # Smaller for faster testing
    dataloader = DataLoader(dataset, batch_size=16, shuffle=False)
    
    # Create sequence packer with epoch simulation
    packer = GreedyBestFitSequencePacker.from_composer(
        src_iterable=dataloader,
        batch_size=64,
        micro_batch_size=16,
        max_seq_len=256,
        buffer_size=320,
        mask_token_id=103,
        pad_token_id=0,
        mask_prob=0.15,
        seed=42,
        simulate_epochs=True,
        tokens_per_epoch=10000,  # Small value for testing
    )
    
    # Test that epoch simulation parameters are set
    if hasattr(packer, 'simulate_epochs') and packer.simulate_epochs:
        print("✅ Epoch simulation enabled")
        print(f"✅ Tokens per epoch: {packer.tokens_per_epoch}")
        
        # Test virtual epoch tracking
        initial_virtual_epoch = packer._virtual_epoch
        initial_tokens_in_epoch = packer._tokens_processed_in_epoch
        
        print(f"Initial virtual epoch: {initial_virtual_epoch}")
        print(f"Initial tokens in epoch: {initial_tokens_in_epoch}")
        
        return True
    else:
        print("❌ Epoch simulation not working")
        return False


def test_batch_iteration():
    """Test that we can iterate through batches without errors."""
    print("\nTesting batch iteration...")
    
    # Create mock dataset and dataloader
    dataset = MockDataset(num_samples=200)
    dataloader = DataLoader(dataset, batch_size=16, shuffle=False)
    
    # Create sequence packer
    packer = GreedyBestFitSequencePacker.from_composer(
        src_iterable=dataloader,
        batch_size=64,
        micro_batch_size=16,
        max_seq_len=256,
        buffer_size=320,
        mask_token_id=103,
        pad_token_id=0,
        mask_prob=0.15,
        seed=42,
    )
    
    try:
        batch_count = 0
        for batch in packer:
            batch_count += 1
            if batch_count >= 5:  # Test first 5 batches
                break
            
            # Verify batch structure
            assert "input_ids" in batch
            assert "labels" in batch
            assert "cu_seqlens" in batch
            assert "max_seqlen" in batch
            assert "attention_mask" in batch
            
            print(f"Batch {batch_count}: input_ids shape = {batch['input_ids'].shape}")
        
        print(f"✅ Successfully processed {batch_count} batches")
        return True
        
    except Exception as e:
        print(f"❌ Error during batch iteration: {e}")
        return False


def main():
    """Run all tests."""
    print("Testing Sequence Packing + Epoch Scheduling Solutions")
    print("=" * 60)
    
    results = []
    
    # Test 1: Length estimation
    results.append(test_sequence_packer_length_estimation())
    
    # Test 2: Epoch simulation
    results.append(test_epoch_simulation())
    
    # Test 3: Basic iteration
    results.append(test_batch_iteration())
    
    print("\n" + "=" * 60)
    print("Test Results Summary:")
    print(f"Length Estimation: {'✅ PASS' if results[0] else '❌ FAIL'}")
    print(f"Epoch Simulation: {'✅ PASS' if results[1] else '❌ FAIL'}")
    print(f"Batch Iteration: {'✅ PASS' if results[2] else '❌ FAIL'}")
    
    if all(results):
        print("\n🎉 All tests passed! Sequence packing with epoch scheduling should work.")
    else:
        print("\n⚠️ Some tests failed. Check the implementation.")
    
    return all(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
