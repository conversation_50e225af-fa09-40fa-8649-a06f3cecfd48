import sys
import os
from omegaconf import Dict<PERSON>onfig, OmegaConf
from transformers import AutoTokenizer
import torch # Required for torch.utils.data used by text_data.py

# --- Mock composer.utils.dist ---
# This is to allow src.text_data (which might use composer.utils.dist) to run in a single-process mode.
class MockComposerDist:
    def get_world_size(self):
        return 8

    def get_rank(self):
        return 0

    def get_global_rank(self):
        return 0

    def is_initialized(self):
        # Return False, as we are not in a DDP setup for this script
        return False
    
    def get_sampler(self, dataset, drop_last, shuffle):
        # Provides a basic sampler for non-distributed context
        from torch.utils.data import RandomSampler, SequentialSampler, DistributedSampler
        if shuffle:
            # If DistributedSampler is used internally for non-DDP, it needs a dataset.
            # Using RandomSampler for clarity in a non-DDP script.
            return RandomSampler(dataset)
        else:
            return SequentialSampler(dataset)

# Attempt to monkey patch composer.utils.dist
# This needs to happen BEFORE src.text_data or other project modules are imported if they use composer.utils.dist
try:
    import composer.utils
    if not hasattr(composer.utils, 'dist_original_backup'): # Simple backup mechanism
        composer.utils.dist_original_backup = composer.utils.dist
    composer.utils.dist = MockComposerDist()
    print("Successfully mocked composer.utils.dist.")
except ImportError:
    print("Warning: mosaicml-composer not found or composer.utils.dist not accessible. Mocking may not be fully effective if src code imports it differently.")
except Exception as e:
    print(f"Warning: Error during composer.utils.dist mocking: {e}")

# Add project root to Python path to allow src imports like `from src.text_data import ...`
# Assumes this script is run from the project root.
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)
# Also add the specific workspace root if known and different, for robustness
# workspace_root = "/home/<USER>/ModernBert_pretrain" # User's workspace
# if workspace_root not in sys.path:
#    sys.path.insert(0, workspace_root)
print(f"Ensured project root ({project_root}) is in sys.path for src imports.")

try:
    from src.text_data import build_text_dataloader
    # Ensure GreedyBestFitSequencePacker is importable if build_text_dataloader relies on it being found via normal imports
    from src.sequence_packer import GreedyBestFitSequencePacker
except ImportError as e:
    print(f"Critical Error: Failed to import from src: {e}")
    print("Please ensure that this script is run from the root of your 'ModernBert_pretrain' project directory,")
    print(" (e.g., /home/<USER>/ModernBert_pretrain/) so that modules like 'src.text_data' can be found.")
    print("Alternatively, ensure 'ModernBert_pretrain' or its 'src' directory is in your PYTHONPATH.")
    sys.exit(1)
except Exception as e:
    print(f"An unexpected error occurred during src imports: {e}")
    sys.exit(1)


def count_batches_for_config(yaml_path: str):
    print(f"Loading configuration from: {yaml_path}")
    try:
        cfg = OmegaConf.load(yaml_path)
    except Exception as e:
        print(f"Error loading YAML file {yaml_path}: {e}")
        sys.exit(1)

    try:
        OmegaConf.resolve(cfg) # Try to resolve interpolations like ${tokenizer_name}
    except Exception as e:
        print(f"Note: Could not fully resolve OmegaConf interpolations: {e}. Proceeding with available values.")

    # Extract necessary parameters, using .get with defaults for safety
    data_local = cfg.get('data_local')
    if not data_local:
        print("Error: 'data_local' not found or is empty in the YAML. This is required.")
        sys.exit(1)
        
    tokenizer_name = cfg.get('tokenizer_name', 'answerdotai/ModernBERT-base')
    max_seq_len = cfg.get('max_seq_len', 1024)
    mlm_probability = cfg.get('mlm_probability', 0.3) # From your YAML's global scope
    
    # Batch sizes from global config
    global_train_batch_size = cfg.get('global_train_batch_size', 2048)
    device_train_microbatch_size = cfg.get('device_train_microbatch_size', 32)

    # === New: Calculate per-GPU batch size for the script ===
    num_gpus_for_script_calculation = 8 
    # This is the per-GPU batch size for the DataLoader that feeds the SequencePacker
    effective_src_batch_size = global_train_batch_size // num_gpus_for_script_calculation
    if global_train_batch_size % num_gpus_for_script_calculation != 0:
        print(f"Warning: global_train_batch_size ({global_train_batch_size}) is not evenly divisible by num_gpus ({num_gpus_for_script_calculation}). Using floor division.")
    # =========================================================

    print("\\nEffective parameters for dataloader construction (for this script simulating one rank out of {num_gpus_for_script_calculation}):")
    print(f"  data_local: {data_local}")
    print(f"  tokenizer_name: {tokenizer_name}")
    print(f"  max_seq_len: {max_seq_len}")
    print(f"  mlm_probability (for dataset): {mlm_probability}")
    print(f"  global_train_batch_size (from YAML): {global_train_batch_size}")
    print(f"  num_gpus_for_script_calculation: {num_gpus_for_script_calculation}")
    print(f"  effective_src_batch_size (for script's source DataLoader): {effective_src_batch_size}")
    print(f"  device_train_microbatch_size (for SequencePacker's output per batch): {device_train_microbatch_size}")

    print(f"\\nInitializing tokenizer: {tokenizer_name}...")
    try:
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
        if tokenizer.pad_token_id is None:
            print("Tokenizer missing pad_token_id. Setting pad_token to eos_token (if available).")
            if tokenizer.eos_token:
                tokenizer.pad_token = tokenizer.eos_token
            else:
                print("Error: Tokenizer has no pad_token and no eos_token to use as pad_token.")
                sys.exit(1)
        # Ensure mask_token is available if mlm_probability > 0
        if mlm_probability > 0 and tokenizer.mask_token_id is None:
            print(f"Error: MLM probability is {mlm_probability} but tokenizer has no mask_token_id.")
            sys.exit(1)

    except Exception as e:
        print(f"Error initializing tokenizer '{tokenizer_name}': {e}")
        sys.exit(1)

    # Prepare the train_loader configuration part from the YAML
    # Ensure it's configured correctly for sequence packing for this script's purpose.
    if not cfg.get('train_loader'):
        print("Error: 'train_loader' configuration block not found in YAML.")
        sys.exit(1)
        
    train_loader_cfg = cfg.train_loader.copy() # Create a mutable copy

    # Explicitly set dataset properties required for sequence packing as understood from src.text_data
    if not hasattr(train_loader_cfg, 'dataset'):
        train_loader_cfg.dataset = OmegaConf.create() # Ensure dataset sub-config exists
        
    train_loader_cfg.dataset.streaming = False # Sequence packing in your setup requires non-streaming
    train_loader_cfg.sequence_packing = True   # Force sequence packing for the count

    # Propagate global settings to dataset sub-config if not already defined there,
    # as build_text_dataloader might expect them within train_loader_cfg.dataset
    train_loader_cfg.dataset.local = train_loader_cfg.dataset.get('local', data_local)
    train_loader_cfg.dataset.tokenizer_name = train_loader_cfg.dataset.get('tokenizer_name', tokenizer_name)
    train_loader_cfg.dataset.max_seq_len = train_loader_cfg.dataset.get('max_seq_len', max_seq_len)
    train_loader_cfg.dataset.mlm_probability = train_loader_cfg.dataset.get('mlm_probability', mlm_probability)
    train_loader_cfg.dataset.remote = train_loader_cfg.dataset.get('remote', cfg.get('data_remote', None))
    train_loader_cfg.dataset.shuffle = train_loader_cfg.dataset.get('shuffle', True) # Default from your YAML

    num_workers_cfg = train_loader_cfg.get('num_workers', 8)
    print(f"Using num_workers for dataloader: {num_workers_cfg} (from YAML, can be set to 0 for easier debugging if issues arise)")

    print("\\nBuilding packed train dataloader (this may take a moment)...")
    
    try:
        packed_dataloader = build_text_dataloader(
            cfg=train_loader_cfg,                            # This is cfg.train_loader with overrides
            tokenizer=tokenizer,                             # Initialized tokenizer object
            device_batch_size=effective_src_batch_size,      # MODIFIED: Use calculated per-GPU source batch size
            device_microbatch_size=device_train_microbatch_size # This becomes the target 'out_batch_size' of the SequencePacker
        )
    except Exception as e:
        print(f"Error calling build_text_dataloader: {e}")
        import traceback
        traceback.print_exc()
        print("\\nPlease check if all arguments to build_text_dataloader are correctly derived and if src/text_data.py is compatible.")
        sys.exit(1)

    print("Iterating through dataloader to count batches (this might take a while depending on dataset size)...")
    num_batches = 0
    try:
        for i, batch in enumerate(packed_dataloader):
            num_batches += 1
            if num_batches == 1 and isinstance(batch, dict): # Basic check on first batch
                print(f"  First batch sample keys: {list(batch.keys())}")
                if "input_ids" in batch:
                    print(f"  First batch input_ids shape: {batch['input_ids'].shape if hasattr(batch['input_ids'], 'shape') else 'N/A'}")
            if num_batches % 200 == 0: # Progress update less frequently for large datasets
                print(f"  ...counted {num_batches} batches")
    except Exception as e:
        print(f"\\nError during dataloader iteration after processing {num_batches} batches: {e}")
        print("This could indicate an issue with the data itself, data loading, or sequence packing logic.")
        import traceback
        traceback.print_exc()
        
    print(f"\\nTotal number of packed batches produced for one epoch: {num_batches}")
    if num_batches == 0:
        print("WARNING: 0 batches were produced. Common causes:")
        print("  - Incorrect 'data_local' path in the YAML or dataset is empty.")
        print("  - Issues with tokenizer vs. data.")
        print("  - 'global_train_batch_size' or 'device_train_microbatch_size' too large for the dataset size.")
        print("  - Problem within 'build_text_dataloader' or 'SequencePacker' logic.")
    return num_batches

if __name__ == "__main__":
    default_yaml_path = "yamls/main/flex-bert-modernbert-base-edu-fw-10B-paper.yaml"
    
    if len(sys.argv) > 1:
        yaml_file_path = sys.argv[1]
        print(f"Using YAML configuration file provided as argument: {yaml_file_path}")
    else:
        yaml_file_path = default_yaml_path
        print(f"No YAML file path provided. Using default: {yaml_file_path}")

    if not os.path.exists(yaml_file_path):
        print(f"Error: YAML file not found at '{os.path.abspath(yaml_file_path)}'")
        print("Please provide the correct path to your YAML configuration file as a command-line argument,")
        print(f"or ensure it exists at the default location relative to the script: {default_yaml_path}")
        sys.exit(1)

    print("--- Batch Counter Script for SequencePacker ---")
    print("This script will load your training data for one epoch using SequencePacker")
    print("to count the total number of packed batches.")
    print("\\nImportant Prerequisites:")
    print("1. Required Python packages: 'omegaconf', 'transformers', 'torch', 'mosaicml-composer'.")
    print("2. Run this script from the root of your 'ModernBert_pretrain' project directory")
    print(f"   (e.g., from /home/<USER>/ModernBert_pretrain/). This helps ensure 'from src.text_data import ...' works.")
    print("3. Your dataset specified in the YAML ('data_local') must be accessible and correctly formatted.")
    print("4. If 'mosaicml-composer' is installed and its 'dist' utilities are imported by your 'src' code,")
    print("   this script attempts to mock them for single-process execution. Check for warnings if this fails.")
    print("-" * 60)
    
    count_batches_for_config(yaml_file_path)
    
    print("-" * 60)
    print("Script finished.")
    print("If the count is 0 or you encountered errors, please check your dataset path, YAML configuration,")
    print("and ensure all dependencies within your 'src' code (like text_data.py, sequence_packer.py) are met.") 